<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <meta charset="UTF-8" />
    <title>SUtils</title>
    <meta
      name="viewport"
      content="viewport-fit=cover, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <meta name="format-detection" content="telephone=no" />
    <meta name="msapplication-tap-highlight" content="no" />

    <link rel="icon" type="image/x-icon" href="./assets/icon/favicon.ico" />
    <link rel="manifest" href="./manifest.json" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@picocss/pico@2/css/pico.orange.min.css">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link rel="stylesheet" href="./css/style.css" />
    <meta name="theme-color" content="#000000" />
  </head>
  <body>
    <main class="container">
      <header>
        <nav>
          <ul>
            <li><strong>SUtils</strong></li>
          </ul>
          <ul>
            <li>
              <button id="configBtn" class="outline secondary" aria-label="Configure API Key">
                <i data-lucide="settings"></i>
              </button>
            </li>
            <li>
              <button id="themeToggle" class="outline secondary" aria-label="Toggle theme">
                <i data-lucide="moon"></i>
              </button>
            </li>
          </ul>
        </nav>
        <hgroup>
          <h1>SUtils</h1>
          <p>Built by soham for soham</p>
        </hgroup>
      </header>

      <!-- Configuration Modal -->
      <dialog id="configModal">
        <article>
          <header>
            <button id="closeModal" aria-label="Close" rel="prev"></button>
            <p>
              <strong>Configuration</strong>
            </p>
          </header>
          <main>
            <label for="apiKey">
              OpenRouter API Key
              <input type="password" id="apiKey" placeholder="sk-or-v1-..." />
            </label>
            <small>
              Get your API key from <a href="https://openrouter.ai/keys" target="_blank">OpenRouter</a>
            </small>

            <label>
              AI Model
              <details id="modelDropdown">
                <summary id="modelSummary">GPT-4o (OpenAI)</summary>
                <ul>
                  <li><a href="#" data-model="openai/gpt-4o">GPT-4o (OpenAI)</a></li>
                  <li><a href="#" data-model="openai/gpt-4o-mini">GPT-4o Mini (OpenAI)</a></li>
                  <li><a href="#" data-model="anthropic/claude-3.5-sonnet">Claude 3.5 Sonnet (Anthropic)</a></li>
                  <li><a href="#" data-model="anthropic/claude-3-haiku">Claude 3 Haiku (Anthropic)</a></li>
                  <li><a href="#" data-model="google/gemini-pro-1.5">Gemini Pro 1.5 (Google)</a></li>
                  <li><a href="#" data-model="meta-llama/llama-3.1-8b-instruct">Llama 3.1 8B (Meta)</a></li>
                  <li><a href="#" data-model="meta-llama/llama-3.1-70b-instruct">Llama 3.1 70B (Meta)</a></li>
                  <li><a href="#" data-model="mistralai/mistral-7b-instruct">Mistral 7B (Mistral AI)</a></li>
                  <li><a href="#" data-model="cohere/command-r-plus">Command R+ (Cohere)</a></li>
                </ul>
              </details>
            </label>
            <small>
              Choose the AI model for text processing. Different models have varying capabilities and costs.
            </small>

            <button style="width:100%;" id="saveApiKey">Save Configuration</button>
          </main>
        </article>
      </dialog>

      <section>
        <h3>Quick Utilities</h3>
        <div class="grid">
          <button class="outline utility-btn" data-task="grammar">
            <i data-lucide="spell-check"></i>
            Fix Grammar
          </button>
          <button class="outline utility-btn" data-task="enhance">
            <i data-lucide="sparkles"></i>
            Enhance Text
          </button>
          <button class="outline utility-btn" data-task="summarize">
            <i data-lucide="file-text"></i>
            Summarize
          </button>
        </div>
      </section>

      <section>
        <h3>Custom Agent</h3>
        <label for="systemPrompt">
          System Prompt
          <textarea id="systemPrompt" placeholder="You are a helpful assistant that..." rows="3"></textarea>
        </label>
      </section>

      <div class="grid">
        <section>
          <h3>Input</h3>
          <textarea id="inputText" placeholder="Enter your text here..." rows="8"></textarea>
          <div class="grid">
            <button id="processBtn">
              <i id="processIcon" data-lucide="play"></i>
              <span id="processText">Process Text</span>
            </button>
            <button id="clearBtn" class="outline">
              <i data-lucide="x"></i>
              Clear
            </button>
          </div>
        </section>

        <section>
          <h3>Output</h3>
          <div id="outputText" class="output-area">Results will appear here...</div>
          <button id="copyBtn" class="outline">
            <i data-lucide="copy"></i>
            Copy
          </button>
        </section>
      </div>

      <section>
        <div id="status" role="status" aria-live="polite"></div>
      </section>
    </main>

    <script src="./js/sutils.js" type="module"></script>
  </body>
</html>
