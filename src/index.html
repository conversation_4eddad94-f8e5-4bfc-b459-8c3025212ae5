<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <meta charset="UTF-8" />
    <title>SUtils</title>
    <meta
      name="viewport"
      content="viewport-fit=cover, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <meta name="format-detection" content="telephone=no" />
    <meta name="msapplication-tap-highlight" content="no" />

    <link rel="icon" type="image/x-icon" href="./assets/icon/favicon.ico" />
    <link rel="manifest" href="./manifest.json" />
    <link rel="stylesheet" href="./css/style.css" />
    <meta name="theme-color" content="#31d53d" />
  </head>
  <body>
    <div class="container">
      <header>
        <h1>SUtils</h1>
        <p>Built by soham for soham</p>
      </header>

      <div class="config-section">
        <h3>Configuration</h3>
        <div class="input-group">
          <label for="apiKey">API Key</label>
          <input type="password" id="apiKey" placeholder="sk-or-v1-..." />
          <button id="saveApiKey" class="secondary-btn">Save</button>
        </div>
      </div>

      <div class="utility-section">
        <h3>Quick Utilities</h3>
        <div class="utility-buttons">
          <button class="utility-btn" data-task="grammar">
            <span>📝</span>
            Fix Grammar
          </button>
          <button class="utility-btn" data-task="enhance">
            <span>✨</span>
            Enhance Text
          </button>
          <button class="utility-btn" data-task="summarize">
            <span>📋</span>
            Summarize
          </button>
          <button class="utility-btn" data-task="translate">
            <span>🌐</span>
            Translate
          </button>
        </div>
      </div>

      <div class="custom-agent-section">
        <h3>Custom Agent</h3>
        <div class="input-group">
          <label for="systemPrompt">System Prompt</label>
          <textarea id="systemPrompt" placeholder="You are a helpful assistant that..." rows="3"></textarea>
        </div>
      </div>

      <div class="main-section">
        <div class="input-section">
          <h3>Input</h3>
          <textarea id="inputText" placeholder="Enter your text here..." rows="8"></textarea>
          <div class="action-buttons">
            <button id="processBtn" class="primary-btn">
              <span id="processIcon">▶</span>
              <span id="processText">Process Text</span>
            </button>
            <button id="clearBtn" class="secondary-btn">Clear</button>
          </div>
        </div>

        <div class="output-section">
          <h3>Output</h3>
          <div id="outputText" class="output-area">Results will appear here...</div>
          <button id="copyBtn" class="secondary-btn">
            <span>📋</span>
            Copy
          </button>
        </div>
      </div>

      <div class="status-section">
        <div id="status" class="status-message"></div>
      </div>
    </div>

    <script src="./js/sutils.js" type="module"></script>
  </body>
</html>
