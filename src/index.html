<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <meta charset="UTF-8" />
    <title>SUtils</title>
    <meta
      name="viewport"
      content="viewport-fit=cover, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <meta name="format-detection" content="telephone=no" />
    <meta name="msapplication-tap-highlight" content="no" />

    <link rel="icon" type="image/x-icon" href="./assets/icon/favicon.ico" />
    <link rel="manifest" href="./manifest.json" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@picocss/pico@2/css/pico.orange.min.css">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link rel="stylesheet" href="./css/style.css" />
    <meta name="theme-color" content="#000000" />
  </head>
  <body>
    <main class="container">
      <header>
        <nav>
          <ul>
            <li><strong>SUtils</strong></li>
          </ul>
          <ul>
            <li>
              <button id="configBtn" class="outline secondary" aria-label="Configure API Key">
                <i data-lucide="settings"></i>
              </button>
            </li>
            <li>
              <button id="themeToggle" class="outline secondary" aria-label="Toggle theme">
                <i data-lucide="moon"></i>
              </button>
            </li>
          </ul>
        </nav>
        <hgroup>
          <h1>SUtils</h1>
          <p>Built by soham for soham</p>
        </hgroup>
      </header>

      <!-- Configuration Modal -->
      <dialog id="configModal">
        <article>
          <header>
            <h3>Configuration</h3>
            <button id="closeModal" class="outline secondary" aria-label="Close">
              <i data-lucide="x"></i>
            </button>
          </header>
          <main>
            <label for="apiKey">
              OpenRouter API Key
              <div class="api-key-input">
                <input type="password" id="apiKey" placeholder="sk-or-v1-..." />
                <button id="saveApiKey" class="outline secondary">Save</button>
              </div>
            </label>
            <small>
              Get your API key from <a href="https://openrouter.ai/keys" target="_blank">OpenRouter</a>
            </small>
          </main>
        </article>
      </dialog>

      <section>
        <h3>Quick Utilities</h3>
        <div class="grid">
          <button class="outline utility-btn" data-task="grammar">
            <i data-lucide="spell-check"></i>
            Fix Grammar
          </button>
          <button class="outline utility-btn" data-task="enhance">
            <i data-lucide="sparkles"></i>
            Enhance Text
          </button>
          <button class="outline utility-btn" data-task="summarize">
            <i data-lucide="file-text"></i>
            Summarize
          </button>
        </div>
      </section>

      <section>
        <h3>Custom Agent</h3>
        <label for="systemPrompt">
          System Prompt
          <textarea id="systemPrompt" placeholder="You are a helpful assistant that..." rows="3"></textarea>
        </label>
      </section>

      <div class="grid">
        <section>
          <h3>Input</h3>
          <textarea id="inputText" placeholder="Enter your text here..." rows="8"></textarea>
          <div class="grid">
            <button id="processBtn">
              <i id="processIcon" data-lucide="play"></i>
              <span id="processText">Process Text</span>
            </button>
            <button id="clearBtn" class="outline">
              <i data-lucide="x"></i>
              Clear
            </button>
          </div>
        </section>

        <section>
          <h3>Output</h3>
          <div id="outputText" class="output-area">Results will appear here...</div>
          <button id="copyBtn" class="outline">
            <i data-lucide="copy"></i>
            Copy
          </button>
        </section>
      </div>

      <section>
        <div id="status" role="status" aria-live="polite"></div>
      </section>
    </main>

    <script src="./js/sutils.js" type="module"></script>
  </body>
</html>
