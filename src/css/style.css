/* Custom styles on top of Pico CSS */

/* API Key input styling */
.api-key-input {
  display: flex;
  gap: 0.5rem;
  align-items: flex-end;
}

.api-key-input input {
  flex: 1;
  margin-bottom: 0;
}

.api-key-input button {
  margin-bottom: 0;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  white-space: nowrap;
}

/* Utility button styling */
.utility-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: flex-start;
}

.utility-btn.active {
  background-color: var(--pico-primary-background);
  color: var(--pico-primary-inverse);
  border-color: var(--pico-primary-background);
}

/* Output area styling */
.output-area {
  min-height: 200px;
  padding: 1rem;
  background-color: var(--pico-card-background-color);
  border: var(--pico-border-width) solid var(--pico-border-color);
  border-radius: var(--pico-border-radius);
  font-family: var(--pico-font-family-monospace);
  font-size: 0.875rem;
  line-height: 1.5;
  white-space: pre-wrap;
  overflow-y: auto;
  margin-bottom: 1rem;
  color: var(--pico-color);
}

/* Button icon spacing */
button i {
  width: 1rem;
  height: 1rem;
}

/* Status message styling */
#status {
  padding: 0.75rem 1rem;
  border-radius: var(--pico-border-radius);
  font-weight: 500;
  text-align: center;
  margin-top: 1rem;
  transition: all 0.2s ease-in-out;
}

#status.success {
  background-color: var(--pico-ins-color);
  color: var(--pico-contrast-inverse);
}

#status.error {
  background-color: var(--pico-del-color);
  color: var(--pico-contrast-inverse);
}

#status.info {
  background-color: var(--pico-primary-background);
  color: var(--pico-primary-inverse);
}

/* Loading animation */
.loading {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--pico-muted-border-color);
  border-radius: 50%;
  border-top-color: var(--pico-primary-background);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
