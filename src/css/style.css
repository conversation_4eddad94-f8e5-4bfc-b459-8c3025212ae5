:root {
  --ion-safe-area-top: env(safe-area-inset-top);
  --ion-safe-area-bottom: env(safe-area-inset-bottom);
  --ion-safe-area-left: env(safe-area-inset-left);
  --ion-safe-area-right: env(safe-area-inset-right);

  /* Light mode colors */
  --background: #ffffff;
  --foreground: #000000;
  --muted: #fafafa;
  --muted-foreground: #737373;
  --border: #e5e5e5;
  --input: #ffffff;
  --primary: #000000;
  --primary-foreground: #ffffff;
  --secondary: #f5f5f5;
  --secondary-foreground: #0a0a0a;
  --accent: #f5f5f5;
  --accent-foreground: #0a0a0a;
  --destructive: #ef4444;
  --destructive-foreground: #fafafa;
  --success: #22c55e;
  --success-foreground: #fafafa;
  --warning: #f59e0b;
  --warning-foreground: #fafafa;
  --info: #3b82f6;
  --info-foreground: #fafafa;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Border radius */
  --radius: 0.5rem;
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
}

/* Dark mode colors */
[data-theme="dark"] {
  --background: #000000;
  --foreground: #ffffff;
  --muted: #0a0a0a;
  --muted-foreground: #a3a3a3;
  --border: #262626;
  --input: #0a0a0a;
  --primary: #ffffff;
  --primary-foreground: #000000;
  --secondary: #171717;
  --secondary-foreground: #fafafa;
  --accent: #171717;
  --accent-foreground: #fafafa;
  --destructive: #ef4444;
  --destructive-foreground: #fafafa;
  --success: #22c55e;
  --success-foreground: #000000;
  --warning: #f59e0b;
  --warning-foreground: #000000;
  --info: #3b82f6;
  --info-foreground: #fafafa;

  /* Dark mode shadows */
  --shadow-sm: 0 1px 2px 0 rgb(255 255 255 / 0.05);
  --shadow: 0 1px 3px 0 rgb(255 255 255 / 0.1), 0 1px 2px -1px rgb(255 255 255 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(255 255 255 / 0.1), 0 2px 4px -2px rgb(255 255 255 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(255 255 255 / 0.1), 0 4px 6px -4px rgb(255 255 255 / 0.1);
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  line-height: 1.5;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  color: var(--foreground);
  background: var(--background);
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 100vh;
}

header {
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid var(--border);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-text {
  text-align: center;
  flex: 1;
}

.header-text h1 {
  margin: 0 0 0.5rem 0;
  color: var(--foreground);
  font-size: 2.25rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.header-text p {
  margin: 0;
  color: var(--muted-foreground);
  font-size: 1rem;
}

.theme-toggle {
  padding: 0.5rem;
  background: var(--secondary);
  color: var(--secondary-foreground);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  font-size: 1.125rem;
}

.theme-toggle:hover {
  background: var(--accent);
  border-color: var(--primary);
}

.config-section,
.utility-section,
.custom-agent-section,
.main-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--background);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border);
  box-shadow: var(--shadow-sm);
}

.config-section h3,
.utility-section h3,
.custom-agent-section h3 {
  margin: 0 0 1rem 0;
  color: var(--foreground);
  font-size: 1.125rem;
  font-weight: 600;
}

.input-group {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  flex-wrap: wrap;
}

.input-group label {
  font-weight: 500;
  color: var(--foreground);
  font-size: 0.875rem;
  min-width: 120px;
}

.input-group input,
.input-group textarea {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  background: var(--input);
  transition: all 0.2s ease-in-out;
  font-family: inherit;
}

.input-group input:focus,
.input-group textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.utility-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 0.75rem;
}

.utility-btn {
  padding: 0.75rem 1rem;
  background: var(--secondary);
  color: var(--secondary-foreground);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.utility-btn:hover {
  background: var(--accent);
  border-color: var(--primary);
}

.utility-btn.active {
  background: var(--primary);
  color: var(--primary-foreground);
  border-color: var(--primary);
}

.main-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  background: var(--background);
  border: none;
  box-shadow: none;
  padding: 0;
}

.input-section,
.output-section {
  padding: 1.5rem;
  background: var(--background);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border);
  box-shadow: var(--shadow-sm);
}

.input-section h3,
.output-section h3 {
  margin: 0 0 1rem 0;
  color: var(--foreground);
  font-size: 1.125rem;
  font-weight: 600;
}

#inputText {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  resize: vertical;
  transition: all 0.2s ease-in-out;
  background: var(--input);
  line-height: 1.5;
}

#inputText:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.output-area {
  width: 100%;
  min-height: 200px;
  padding: 0.75rem;
  background: var(--muted);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  line-height: 1.5;
  white-space: pre-wrap;
  overflow-y: auto;
  margin-bottom: 1rem;
  color: var(--foreground);
}

.action-buttons {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
}

.primary-btn {
  padding: 0.625rem 1.5rem;
  background: var(--primary);
  color: var(--primary-foreground);
  border: 1px solid var(--primary);
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  flex: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.primary-btn:hover:not(:disabled) {
  background: rgba(0, 0, 0, 0.9);
  border-color: rgba(0, 0, 0, 0.9);
}

.primary-btn:disabled {
  background: var(--muted);
  color: var(--muted-foreground);
  border-color: var(--border);
  cursor: not-allowed;
  opacity: 0.6;
}

.secondary-btn {
  padding: 0.625rem 1.5rem;
  background: var(--secondary);
  color: var(--secondary-foreground);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.secondary-btn:hover {
  background: var(--accent);
  border-color: var(--primary);
}

.status-section {
  margin-top: 1.5rem;
}

.status-message {
  padding: 0.75rem 1rem;
  border-radius: var(--radius);
  font-weight: 500;
  font-size: 0.875rem;
  text-align: center;
  transition: all 0.2s ease-in-out;
  border: 1px solid transparent;
}

.status-message.success {
  background: rgba(34, 197, 94, 0.1);
  color: var(--success);
  border-color: rgba(34, 197, 94, 0.2);
}

.status-message.error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--destructive);
  border-color: rgba(239, 68, 68, 0.2);
}

.status-message.info {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info);
  border-color: rgba(59, 130, 246, 0.2);
}

/* Loading animation */
.loading {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--muted);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .header-text {
    order: 2;
  }

  .theme-toggle {
    order: 1;
    align-self: flex-end;
  }

  .main-section {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .utility-buttons {
    grid-template-columns: 1fr;
  }

  .input-group {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .input-group label {
    min-width: auto;
  }

  .action-buttons {
    flex-direction: column;
  }

  .header-text h1 {
    font-size: 1.875rem;
  }
}
