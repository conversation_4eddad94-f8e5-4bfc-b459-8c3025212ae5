<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":capacitor-cordova-android-plugins" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/soham901/sutils/android/capacitor-cordova-android-plugins/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":capacitor-android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/soham901/sutils/node_modules/.pnpm/@capacitor+android@7.4.2_@capacitor+core@7.4.2/node_modules/@capacitor/android/capacitor/build/intermediates/library_assets/debug/packageDebugAssets/out"><file name="native-bridge.js" path="/home/<USER>/soham901/sutils/node_modules/.pnpm/@capacitor+android@7.4.2_@capacitor+core@7.4.2/node_modules/@capacitor/android/capacitor/build/intermediates/library_assets/debug/packageDebugAssets/out/native-bridge.js"/></source></dataSet><dataSet config=":capacitor-splash-screen" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/soham901/sutils/node_modules/.pnpm/@capacitor+splash-screen@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/splash-screen/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":capacitor-camera" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/soham901/sutils/node_modules/.pnpm/@capacitor+camera@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/camera/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/soham901/sutils/android/app/src/main/assets"><file name="capacitor.plugins.json" path="/home/<USER>/soham901/sutils/android/app/src/main/assets/capacitor.plugins.json"/><file name="capacitor.config.json" path="/home/<USER>/soham901/sutils/android/app/src/main/assets/capacitor.config.json"/><file name="public/cordova.js" path="/home/<USER>/soham901/sutils/android/app/src/main/assets/public/cordova.js"/><file name="public/assets/favicon-UvKAN7pv.ico" path="/home/<USER>/soham901/sutils/android/app/src/main/assets/public/assets/favicon-UvKAN7pv.ico"/><file name="public/assets/manifest-RD-sWVQA.json" path="/home/<USER>/soham901/sutils/android/app/src/main/assets/public/assets/manifest-RD-sWVQA.json"/><file name="public/assets/index-C5y_fcH4.css" path="/home/<USER>/soham901/sutils/android/app/src/main/assets/public/assets/index-C5y_fcH4.css"/><file name="public/assets/web-BcvC_eV0.js" path="/home/<USER>/soham901/sutils/android/app/src/main/assets/public/assets/web-BcvC_eV0.js"/><file name="public/assets/index-DPR9rxeD.js" path="/home/<USER>/soham901/sutils/android/app/src/main/assets/public/assets/index-DPR9rxeD.js"/><file name="public/cordova_plugins.js" path="/home/<USER>/soham901/sutils/android/app/src/main/assets/public/cordova_plugins.js"/><file name="public/index.html" path="/home/<USER>/soham901/sutils/android/app/src/main/assets/public/index.html"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/soham901/sutils/android/app/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/soham901/sutils/android/app/build/intermediates/shader_assets/debug/compileDebugShaders/out"/></dataSet></merger>